import React, { useEffect } from "react";
import { api } from "@/helpers/api";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQueryClient } from "@tanstack/react-query";
import { ToastBar, Toaster, toast } from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { useNormalStore, useSessionStore, useSocketStore } from "../app/store/stores";
import { NotificationHandler } from "./NotificationHandler";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";

// Define types for socket notification message
interface NotificationMessage {
    type: string;
    details: string | Record<string, unknown>;
}

// Helper function to get toast styling based on type
const getToastStyle = (type: string) => {
    switch (type) {
        case "success":
            return {
                icon: CheckCircle,
                iconColor: "text-green-300",
                gradient: "from-green-900/90 via-green-800/80 to-emerald-900/90",
                borderColor: "border-green-400/30",
                accentGradient: "from-green-500 to-emerald-400",
                glowColor: "shadow-green-500/20",
            };
        case "error":
            return {
                icon: AlertCircle,
                iconColor: "text-red-300",
                gradient: "from-red-900/90 via-red-800/80 to-rose-900/90",
                borderColor: "border-red-400/30",
                accentGradient: "from-red-500 to-rose-400",
                glowColor: "shadow-red-500/20",
            };
        case "loading":
            return {
                icon: Info,
                iconColor: "text-blue-300",
                gradient: "from-blue-900/90 via-blue-800/80 to-indigo-900/90",
                borderColor: "border-blue-400/30",
                accentGradient: "from-blue-500 to-indigo-400",
                glowColor: "shadow-blue-500/20",
            };
        default:
            return {
                icon: Info,
                iconColor: "text-purple-300",
                gradient: "from-purple-900/90 via-purple-800/80 to-violet-900/90",
                borderColor: "border-purple-400/30",
                accentGradient: "from-purple-500 to-violet-400",
                glowColor: "shadow-purple-500/20",
            };
    }
};

const ToastManager = () => {
    const { socket } = useSocketStore();
    const { setJustJailed } = useNormalStore();
    const { setLevelupValue } = useSessionStore();
    const queryClient = useQueryClient();
    const location = useLocation();
    const navigate = useNavigate();
    const isMobile = useCheckMobileScreen();

    useEffect(() => {
        if (socket) {
            socket.on("notification", (msg: NotificationMessage) => {
                if (msg.type === "jail") {
                    setJustJailed(true);
                }
                if (msg.type === "message") {
                    if (location.pathname.includes("/inbox")) {
                        queryClient.invalidateQueries({
                            queryKey: api.messaging.getChatHistory.key(),
                        });
                        return;
                    }
                }

                queryClient.invalidateQueries({
                    queryKey: api.notifications.getList.key(),
                });
                const details = msg.details;
                NotificationHandler(msg.type, details, navigate, queryClient, setLevelupValue);
            });
            return () => {
                socket.off("notification");
            };
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [socket]);

    let containerStyle: React.CSSProperties = {};
    if (isMobile || !socket) {
        containerStyle = {
            top: 65,
        };
    } else {
        containerStyle = {
            top: 90,
            right: "26rem",
        };
    }

    return (
        <Toaster
            position={isMobile ? "top-center" : "top-right"}
            containerStyle={containerStyle}
            toastOptions={{
                duration: 4000,
                style: {
                    background: "transparent",
                    border: "none",
                    boxShadow: "none",
                    padding: 0,
                    maxWidth: isMobile ? "20rem" : "24rem",
                },
            }}
        >
            {(t) => (
                <ToastBar toast={t}>
                    {({ icon, message }) => {
                        const toastStyle = getToastStyle(t.type);
                        const IconComponent = toastStyle.icon;

                        return (
                            <div
                                className={`
                                    relative overflow-hidden rounded-xl border backdrop-blur-md
                                    bg-gradient-to-br ${toastStyle.gradient}
                                    ${toastStyle.borderColor} ${toastStyle.glowColor}
                                    shadow-xl cursor-pointer group
                                    hover:scale-[1.02] transition-all duration-200 ease-out
                                    ${isMobile ? "max-w-sm mx-2" : "max-w-md"}
                                `}
                                onClick={() => toast.dismiss(t.id)}
                            >
                                {/* Subtle overlay for depth */}
                                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10 pointer-events-none" />

                                {/* Content */}
                                <div className="relative flex items-start gap-3 p-4">
                                    {/* Icon */}
                                    <div className={`flex-shrink-0 ${toastStyle.iconColor} drop-shadow-sm`}>
                                        <IconComponent size={20} strokeWidth={1.5} />
                                    </div>

                                    {/* Message */}
                                    <div className="flex-1 min-w-0">
                                        <div className="text-white text-sm font-medium leading-relaxed drop-shadow-sm">
                                            {message}
                                        </div>
                                    </div>

                                    {/* Close button */}
                                    <button
                                        className="flex-shrink-0 text-white/60 hover:text-white transition-colors duration-200 p-1.5 rounded-lg hover:bg-white/10 group-hover:opacity-100 opacity-70"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toast.dismiss(t.id);
                                        }}
                                    >
                                        <X size={16} strokeWidth={1.5} />
                                    </button>
                                </div>

                                {/* Progress bar */}
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20 overflow-hidden">
                                    <div
                                        className={`h-full bg-gradient-to-r ${toastStyle.accentGradient} transition-all duration-75 ease-linear`}
                                        style={{
                                            width: `${((4000 - (Date.now() - t.createdAt)) / 4000) * 100}%`,
                                            transition: t.visible ? "width 4000ms linear" : "none",
                                        }}
                                    />
                                </div>
                            </div>
                        );
                    }}
                </ToastBar>
            )}
        </Toaster>
    );
};

export default ToastManager;
